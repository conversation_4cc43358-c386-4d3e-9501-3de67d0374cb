{% extends "base.html" %}

{% from "components/page_header.html" import page_header %}
{% from "components/button.html" import button, button_group %}
{% from "partials/forms/base_form.html" import form_group %}
{% from "components/modal.html" import modal %}

{% block title %}{{ title }}{% endblock %}

{% block header %}{{ title }}{% endblock %}

{% block content %}
<div class="space-y-6">
  <!-- Page Header -->
  {{ page_header(
    title=title,
    button_text="Request Attendance",
    button_icon="plus",
    button_action="openAttendanceRequestForm()",
    description="View your attendance records in calendar format"
  ) }}

  <!-- Quick Actions -->
  {% set quick_action_buttons = [
    {"text": "List View", "variant": "outline", "href": url_for('attendance.my_attendance'), "icon": "list"},
    {"text": "Test Modal", "variant": "outline", "onclick": "testModal()", "icon": "test-tube"}
  ] %}
  {{ button_group(quick_action_buttons) }}



  <!-- Calendar Card -->
  <div class="card">
    <div class="card-header">
      <div class="flex items-center justify-between">
        <h2 class="card-title" id="calendarTitle">Loading...</h2>
        <div class="flex items-center space-x-2">
          {% set nav_buttons = [
            {"text": "", "variant": "outline", "size": "sm", "icon": "chevron-left", "onclick": "navigateToPreviousMonth()", "title": "Previous Month"},
            {"text": "Today", "variant": "outline", "size": "sm", "onclick": "navigateToToday()"},
            {"text": "", "variant": "outline", "size": "sm", "icon": "chevron-right", "onclick": "navigateToNextMonth()", "title": "Next Month"}
          ] %}
          {{ button_group(nav_buttons) }}
        </div>
      </div>
    </div>

    <div class="card-content">
      <div id="calendarContainer">
        <!-- Calendar will be rendered here by JavaScript -->
        <div class="text-center py-8">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p class="mt-2 text-sm text-muted-foreground">Loading calendar...</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Legend -->
  <div class="card">
    <div class="card-header">
      <h3 class="card-title">Legend</h3>
    </div>
    <div class="card-content">
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div class="flex items-center">
          <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
          <span class="text-sm">Approved</span>
        </div>
        <div class="flex items-center">
          <div class="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
          <span class="text-sm">Pending</span>
        </div>
        <div class="flex items-center">
          <div class="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
          <span class="text-sm">Rejected</span>
        </div>
        <div class="flex items-center">
          <div class="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
          <span class="text-sm">Holiday</span>
        </div>
      </div>
      <div class="mt-4 pt-4 border-t border-border">
        <p class="text-xs text-muted-foreground">
          💡 Hover over dates for details, click for more information
        </p>
      </div>
    </div>
  </div>
</div>

<!-- Hidden data for JavaScript -->
<input type="hidden" id="attendanceApiUrl" value="{{ url_for('api.get_user_attendance_calendar') }}">
<input type="hidden" id="holidayApiUrl" value="{{ url_for('api.check_holiday_simple') }}">
<input type="hidden" id="currentUserRegion" value="{{ user_region }}">

<!-- Date Information Modal -->
{% call modal(
  id='calendar-date-info',
  title='Date Information',
  description='Details for the selected date',
  size='md',
  show_footer=false
) %}
  <div id="calendar-modal-content" class="space-y-4">
    <!-- Content will be populated by JavaScript -->
    <div class="text-center py-4">
      <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto"></div>
      <p class="mt-2 text-sm text-muted-foreground">Loading...</p>
    </div>
  </div>
{% endcall %}

<script>
document.addEventListener('DOMContentLoaded', function() {
  const calendarContainer = document.getElementById('calendarContainer');
  const calendarTitle = document.getElementById('calendarTitle');

  let currentDate = new Date();
  let attendanceData = {};
  let holidayData = {};

  // Update calendar title
  function updateCalendarTitle() {
    const monthNames = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    calendarTitle.textContent = `${monthNames[currentDate.getMonth()]} ${currentDate.getFullYear()}`;
  }

  // Load attendance data for the month
  async function loadAttendanceData() {
    try {
      const year = currentDate.getFullYear();
      const month = currentDate.getMonth() + 1;
      const userRegion = document.getElementById('currentUserRegion').value || 'PH';

      // Load user's attendance data
      const attendanceApiUrl = document.getElementById('attendanceApiUrl').value;
      const attendanceResponse = await fetch(`${attendanceApiUrl}?year=${year}&month=${month}`);
      if (attendanceResponse.ok) {
        const attendanceResult = await attendanceResponse.json();
        if (attendanceResult.success) {
          attendanceData = attendanceResult.calendar_data || {};
        } else {
          console.error('Failed to load attendance data:', attendanceResult.error);
          attendanceData = {};
        }
      } else {
        console.error('Failed to fetch attendance data');
        attendanceData = {};
      }

      // Load holiday data for user's region
      const holidayResponse = await fetch(`/api/holidays/calendar/${userRegion}/${year}?month=${month}`);
      if (holidayResponse.ok) {
        const holidayResult = await holidayResponse.json();
        if (holidayResult.success) {
          holidayData = holidayResult.calendar_data || {};
        } else {
          console.error('Failed to load holiday data:', holidayResult.error);
          holidayData = {};
        }
      } else {
        console.error('Failed to fetch holiday data');
        holidayData = {};
      }
    } catch (error) {
      console.error('Error loading calendar data:', error);
      attendanceData = {};
      holidayData = {};
    }
  }

  // Render calendar
  function renderCalendar() {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();

    // Get first day of month and number of days
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    let html = `
      <div class="grid grid-cols-7 gap-1">
        <!-- Day headers -->
        <div class="p-2 text-center text-sm font-medium text-gray-500 dark:text-gray-400">Sun</div>
        <div class="p-2 text-center text-sm font-medium text-gray-500 dark:text-gray-400">Mon</div>
        <div class="p-2 text-center text-sm font-medium text-gray-500 dark:text-gray-400">Tue</div>
        <div class="p-2 text-center text-sm font-medium text-gray-500 dark:text-gray-400">Wed</div>
        <div class="p-2 text-center text-sm font-medium text-gray-500 dark:text-gray-400">Thu</div>
        <div class="p-2 text-center text-sm font-medium text-gray-500 dark:text-gray-400">Fri</div>
        <div class="p-2 text-center text-sm font-medium text-gray-500 dark:text-gray-400">Sat</div>
    `;

    // Empty cells for days before month starts
    for (let i = 0; i < startingDayOfWeek; i++) {
      html += '<div class="p-2 h-24"></div>';
    }

    // Days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      const isToday = new Date().toDateString() === new Date(year, month, day).toDateString();
      const hasAttendance = attendanceData[dateStr];
      const isHoliday = holidayData[dateStr];

      let dayClass = 'p-2 h-24 border border-gray-200 dark:border-gray-600 relative cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors';

      // Today styling
      if (isToday) {
        dayClass += ' bg-blue-50 dark:bg-blue-900/20 border-blue-300 dark:border-blue-600';
      }

      // Holiday styling
      if (isHoliday) {
        dayClass += ' bg-blue-50 dark:bg-blue-900/10 border-blue-200 dark:border-blue-800';
      }

      let indicators = '';
      let tooltipText = `${dateStr}`;

      // Holiday indicator
      if (isHoliday) {
        indicators += '<div class="absolute top-1 right-1 w-2 h-2 bg-blue-500 rounded-full" title="Holiday"></div>';
        tooltipText += `\nHoliday: ${isHoliday.name}`;
        if (isHoliday.description) {
          tooltipText += `\n${isHoliday.description}`;
        }
      }

      // Attendance indicator
      if (hasAttendance) {
        const statusColor = {
          'pending': 'bg-yellow-500',
          'approved': 'bg-green-500',
          'rejected': 'bg-red-500',
          'auto_approved': 'bg-green-500',
          'cancelled': 'bg-gray-500'
        }[hasAttendance.status] || 'bg-gray-500';
        indicators += `<div class="absolute bottom-1 left-1 w-2 h-2 ${statusColor} rounded-full" title="Attendance: ${hasAttendance.status_display}"></div>`;
        tooltipText += `\nAttendance: ${hasAttendance.type} (${hasAttendance.status_display})`;
        if (hasAttendance.start_time && hasAttendance.end_time) {
          tooltipText += `\nTime: ${hasAttendance.start_time} - ${hasAttendance.end_time}`;
        }
      }

      html += `
        <div class="${dayClass}" onclick="selectDate('${dateStr}')" title="${tooltipText}">
          <div class="text-sm ${isToday ? 'font-bold text-blue-600 dark:text-blue-400' : 'text-gray-900 dark:text-white'}">${day}</div>
          ${indicators}
          ${isHoliday ? `<div class="text-xs text-blue-600 dark:text-blue-400 mt-1 truncate font-medium">${isHoliday.name}</div>` : ''}
          ${hasAttendance ? `<div class="text-xs text-gray-600 dark:text-gray-400 mt-1 truncate">${hasAttendance.type}</div>` : ''}
        </div>
      `;
    }

    html += '</div>';
    calendarContainer.innerHTML = html;
  }

  // Select date handler
  window.selectDate = function(dateStr) {
    console.log('selectDate called with:', dateStr);
    console.log('showModal function available:', typeof window.showModal);

    const hasAttendance = attendanceData[dateStr];
    const isHoliday = holidayData[dateStr];
    const selectedDate = new Date(dateStr);
    const formattedDate = selectedDate.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    // Build modal content
    let content = `<div class="space-y-4">`;
    content += `<div class="flex items-center space-x-2">`;
    content += `<i data-lucide="calendar" class="h-5 w-5 text-muted-foreground"></i>`;
    content += `<span class="font-medium">${formattedDate}</span>`;
    content += `</div>`;

    if (isHoliday) {
      content += `<div class="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-4">`;
      content += `<div class="flex items-start space-x-3">`;
      content += `<i data-lucide="calendar-heart" class="h-5 w-5 text-orange-600 dark:text-orange-400 mt-0.5"></i>`;
      content += `<div class="space-y-1">`;
      content += `<h4 class="font-medium text-orange-800 dark:text-orange-200">Holiday: ${isHoliday.name}</h4>`;
      if (isHoliday.description) {
        content += `<p class="text-sm text-orange-700 dark:text-orange-300">${isHoliday.description}</p>`;
      }
      content += `<p class="text-xs text-orange-600 dark:text-orange-400">Region: ${isHoliday.region_code}</p>`;
      content += `</div></div></div>`;
    }

    if (hasAttendance) {
      content += `<div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">`;
      content += `<div class="flex items-start space-x-3">`;
      content += `<i data-lucide="clipboard-list" class="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5"></i>`;
      content += `<div class="space-y-2">`;
      content += `<h4 class="font-medium text-blue-800 dark:text-blue-200">Attendance Record</h4>`;
      content += `<div class="space-y-1 text-sm">`;
      content += `<div><span class="font-medium">Type:</span> ${hasAttendance.type}</div>`;
      content += `<div><span class="font-medium">Status:</span> <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-${hasAttendance.status === 'approved' ? 'green' : hasAttendance.status === 'pending' ? 'yellow' : 'red'}-100 text-${hasAttendance.status === 'approved' ? 'green' : hasAttendance.status === 'pending' ? 'yellow' : 'red'}-800">${hasAttendance.status_display}</span></div>`;
      if (hasAttendance.start_time && hasAttendance.end_time) {
        content += `<div><span class="font-medium">Time:</span> ${hasAttendance.start_time} - ${hasAttendance.end_time}</div>`;
      }
      if (hasAttendance.duration_hours) {
        content += `<div><span class="font-medium">Duration:</span> ${hasAttendance.duration_hours} hours</div>`;
      }
      if (hasAttendance.notes) {
        content += `<div><span class="font-medium">Notes:</span> ${hasAttendance.notes}</div>`;
      }
      content += `</div></div></div></div>`;
    }

    if (!hasAttendance && !isHoliday) {
      content += `<div class="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">`;
      content += `<div class="flex items-start space-x-3">`;
      content += `<i data-lucide="calendar-plus" class="h-5 w-5 text-gray-500 dark:text-gray-400 mt-0.5"></i>`;
      content += `<div class="space-y-1">`;
      content += `<p class="text-sm text-gray-600 dark:text-gray-300">No attendance records or holidays on this date.</p>`;
      content += `<p class="text-xs text-gray-500 dark:text-gray-400">Click "Request Attendance" to add a record for this date.</p>`;
      content += `</div></div></div>`;
    }

    content += `</div>`;

    // Show the modal with the updated content
    console.log('Attempting to show modal...');

    // Function to show modal when ready
    function showModalWhenReady() {
      if (typeof window.showModal === 'function') {
        console.log('showModal function is available');
        const template = document.getElementById('calendar-date-info-template');
        console.log('Template found:', !!template);
        if (template) {
          // Clone the template content and update it
          const tempDiv = document.createElement('div');
          tempDiv.innerHTML = template.innerHTML;

          // Find the modal content area and update it
          const modalContentArea = tempDiv.querySelector('#calendar-modal-content');
          console.log('Modal content area found:', !!modalContentArea);
          if (modalContentArea) {
            modalContentArea.innerHTML = content;
          }

          console.log('Calling showModal with content...');
          // Show the modal with the updated content
          try {
            window.showModal({
              id: 'calendar-date-info',
              content: tempDiv.innerHTML,
              size: template.getAttribute('data-size') || 'md'
            });
            console.log('Modal shown successfully');
          } catch (error) {
            console.error('Error showing modal:', error);
          }
        } else {
          console.error('Modal template calendar-date-info-template not found');
        }
      } else {
        console.error('showModal function not available, retrying in 100ms...');
        setTimeout(showModalWhenReady, 100);
      }
    }

    showModalWhenReady();
  };

  // Navigation functions (called by buttons)
  window.navigateToPreviousMonth = function() {
    currentDate.setMonth(currentDate.getMonth() - 1);
    updateCalendar();
  };

  window.navigateToNextMonth = function() {
    currentDate.setMonth(currentDate.getMonth() + 1);
    updateCalendar();
  };

  window.navigateToToday = function() {
    currentDate = new Date();
    updateCalendar();
  };

  // Open attendance request form
  window.openAttendanceRequestForm = function() {
    // Route to my records page and open drawer
    window.location.href = '{{ url_for("attendance.my_attendance") }}?open_drawer=true';
  };

  // Test modal function
  window.testModal = function() {
    console.log('Test modal function called');
    if (typeof window.showModal === 'function') {
      const template = document.getElementById('calendar-date-info-template');
      if (template) {
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = template.innerHTML;

        const modalContentArea = tempDiv.querySelector('#calendar-modal-content');
        if (modalContentArea) {
          modalContentArea.innerHTML = '<p>This is a test modal to verify the modal system is working!</p>';
        }

        window.showModal({
          id: 'calendar-date-info',
          content: tempDiv.innerHTML,
          size: 'md'
        });
      } else {
        alert('Modal template not found');
      }
    } else {
      alert('showModal function not available');
    }
  };

  // Update calendar
  async function updateCalendar() {
    updateCalendarTitle();
    await loadAttendanceData();
    renderCalendar();
  }

  // Initialize
  updateCalendar();
});
</script>
{% endblock %}
